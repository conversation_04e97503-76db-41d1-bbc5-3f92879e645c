services:
  postgres:
    image: postgres:17-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB_DEV}
      POSTGRES_USER: ${POSTGRES_USER_DEV}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD_DEV}

  backend:
    build:
      context: .
      dockerfile: .docker/Backend/Dockerfile
    env_file: .env
    environment:
      PORT: 3000
    depends_on:
      - postgres
    volumes:
      - ./Backend:/app
      - backend_node_modules:/app/node_modules
    networks:
      - intranet
    tmpfs:
      - /tmp

  frontend:
    build:
      context: .
      dockerfile: .docker/Frontend/Dockerfile
    env_file: .env
    volumes:
      - ./Frontend:/app
      - frontend_node_modules:/app/node_modules
      - frontend_sveltekit:/app/.svelte-kit
    networks:
      - intranet

  nginx:
    image: nginx:1.27-alpine
    depends_on:
      - frontend
      - backend
    ports:
      - "80:80"
    volumes:
      - ./.docker/nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
    networks:
      - intranet
volumes:
  frontend_node_modules:
  frontend_sveltekit:
  backend_node_modules:
networks:
  intranet:
    driver: bridge
