events { worker_connections 1024; }

http {
  server {
    listen 80;

    # --- Frontend ---
    location / {
      proxy_pass http://frontend:5173;
      proxy_http_version 1.1;

      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;

      proxy_set_header Host $host;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

      proxy_read_timeout 3600s;
      proxy_send_timeout 3600s;
    }

    # --- Backend ---
    location /api/ {
      proxy_pass http://backend:3000/;
      proxy_http_version 1.1;

      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;

      proxy_set_header Host $host;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
  }

  map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
  }
}
