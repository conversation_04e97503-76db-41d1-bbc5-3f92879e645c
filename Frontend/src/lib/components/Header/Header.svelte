<script lang="ts">
	import HeaderNav from '$lib/components/Header/HeaderNav.svelte';
	import Button from '$lib/components/primitives/Button/Button.svelte';
	import HeaderLogo from '$lib/components/Header/HeaderLogo.svelte';
	import Flex from '$lib/components/primitives/Flex/Flex.svelte';
	import { Key } from '@lucide/svelte';
</script>

<header>
	<Flex grow={1} justify="start" align="center">
		<HeaderLogo />
	</Flex>

	<Flex grow={1} justify="center" align="center">
		<HeaderNav />
	</Flex>

	<Flex grow={1} justify="end" align="center">
		<Button variant="primary">
			Přihlásit se
			<Key size="1rem" />
		</Button>
	</Flex>
</header>

<style lang="scss">
	header {
		display: flex;
		align-items: center;
		padding: var(--spacing-m) 0;
	}
</style>
