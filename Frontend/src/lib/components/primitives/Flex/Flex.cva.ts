import { cva } from 'class-variance-authority';

export const flexCva = cva(['flex'], {
	variants: {
		inline: {
			false: 'flex',
			true: 'inline-flex'
		},
		direction: {
			row: 'flex-row',
			'row-reverse': 'flex-row-reverse',
			col: 'flex-col',
			'col-reverse': 'flex-col-reverse'
		},
		align: {
			start: 'items-start',
			center: 'items-center',
			end: 'items-end',
			stretch: 'items-stretch',
			baseline: 'items-baseline'
		},
		justify: {
			start: 'justify-start',
			center: 'justify-center',
			end: 'justify-end',
			between: 'justify-between',
			around: 'justify-around',
			evenly: 'justify-evenly'
		},
		wrap: {
			nowrap: 'flex-nowrap',
			wrap: 'flex-wrap',
			'wrap-reverse': 'flex-wrap-reverse'
		},
		gap: {
			none: '',
			xs: 'gap-xs',
			s: 'gap-s',
			sm: 'gap-sm',
			m: 'gap-m',
			l: 'gap-l',
			xl: 'gap-xl',
			xxl: 'gap-xxl'
		},
		grow: {
			0: 'grow-0',
			1: 'grow'
		},
		shrink: {
			0: 'shrink-0',
			1: 'shrink'
		}
	},
	defaultVariants: {
		inline: false,
		direction: 'row',
		align: 'stretch',
		justify: 'start',
		wrap: 'nowrap',
		gap: 'none',
		grow: 0,
		shrink: 1
	}
});
