import type { VariantProps } from 'class-variance-authority';
import type { HTMLAttributes } from 'svelte/elements';

import type { flexCva } from '$lib/components/primitives/Flex/Flex.cva';

type Variants = VariantProps<typeof flexCva>;

export type FlexDivProps = { as?: 'div' } & HTMLAttributes<HTMLDivElement> & Variants;

export type FlexSpanProps = { as: 'span' } & HTMLAttributes<HTMLSpanElement> & Variants;

export type FlexProps = FlexDivProps | FlexSpanProps;
