<script lang="ts">
	import clsx from 'clsx';

	import type { FlexProps } from '$lib/components/primitives/Flex/Flex';
	import { flexCva } from '$lib/components/primitives/Flex/Flex.cva';

	let {
		children,
		as = 'div',
		inline,
		direction,
		align,
		justify,
		wrap,
		gap,
		grow,
		shrink,
		class: className,
		style,
		...props
	}: FlexProps = $props();
</script>

<svelte:element
	this={as}
	class={clsx(flexCva({ inline, direction, align, justify, wrap, gap, grow, shrink }), className)}
	{...props}
>
	{@render children?.()}
</svelte:element>

<style lang="scss">
	.flex {
		display: flex;
	}

	.inline-flex {
		display: inline-flex;
	}

	.flex-row {
		flex-direction: row;
	}

	.flex-row-reverse {
		flex-direction: row-reverse;
	}

	.flex-col {
		flex-direction: column;
	}

	.flex-col-reverse {
		flex-direction: column-reverse;
	}

	.items-start {
		align-items: flex-start;
	}

	.items-center {
		align-items: center;
	}

	.items-end {
		align-items: flex-end;
	}

	.items-stretch {
		align-items: stretch;
	}

	.items-baseline {
		align-items: baseline;
	}

	.justify-start {
		justify-content: flex-start;
	}

	.justify-center {
		justify-content: center;
	}

	.justify-end {
		justify-content: flex-end;
	}

	.justify-between {
		justify-content: space-between;
	}

	.justify-around {
		justify-content: space-around;
	}

	.justify-evenly {
		justify-content: space-evenly;
	}

	.flex-nowrap {
		flex-wrap: nowrap;
	}

	.flex-wrap {
		flex-wrap: wrap;
	}

	.flex-wrap-reverse {
		flex-wrap: wrap-reverse;
	}

	.grow {
		flex-grow: 1;
	}

	.grow-0 {
		flex-grow: 0;
	}

	.shrink {
		flex-shrink: 1;
	}

	.shrink-0 {
		flex-shrink: 0;
	}

	.gap {
		&-xs {
			gap: var(--spacing-xs);
		}

		&-s {
			gap: var(--spacing-s);
		}

		&-sm {
			gap: var(--spacing-sm);
		}

		&-m {
			gap: var(--spacing-m);
		}

		&-l {
			gap: var(--spacing-l);
		}

		&-xl {
			gap: var(--spacing-xl);
		}
	}
</style>
