import { cva } from 'class-variance-authority';

export const textCva = cva('text', {
	variants: {
		as: {
			p: '',
			span: '',
			div: '',
			label: ''
		},
		align: {
			left: 'text-align-left',
			center: 'text-align-center',
			right: 'text-align-right'
		},
		wrap: {
			wrap: 'text-wrap-wrap',
			nowrap: 'text-wrap-nowrap',
			pretty: 'text-wrap-pretty',
			balance: 'text-wrap-balance'
		},
		size: {
			regular: 'text-size-regular',
			small: 'text-size-small'
		},
		italic: {
			true: 'text-italic',
			false: ''
		},
		color: {
			default: 'text-color-default',
			inherited: 'text-color-inherited',
			secondary: 'text-color-secondary'
		},
		weight: {
			normal: 'text-weight-normal',
			bold: 'text-weight-bold'
		}
	},
	defaultVariants: {
		as: 'p',
		align: 'left',
		wrap: 'wrap',
		size: 'regular',
		italic: false,
		color: 'default',
		weight: 'normal'
	}
});
