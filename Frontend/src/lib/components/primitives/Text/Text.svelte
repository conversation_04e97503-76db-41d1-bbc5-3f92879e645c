<script lang="ts">
	import clsx from 'clsx';

	import type { TextProps } from '$lib/components/primitives/Text/Text';
	import { textCva } from '$lib/components/primitives/Text/Text.cva';

	let { children, as, align, wrap, size, italic, color, weight, ...props }: TextProps = $props();
</script>

<svelte:element
	this={as}
	class={clsx(textCva({ align, wrap, size, italic, color, weight }))}
	{...props}
>
	{@render children?.()}
</svelte:element>

<style lang="scss">
	.text {
		margin: 0;
		font-family: inherit;
		line-height: 1.5;
	}

	.text-size {
		&-regular {
			font-size: var(--font-size-text);
		}

		&-small {
			font-size: var(--font-size-sub);
		}
	}

	.text-align {
		&-left {
			text-align: left;
		}

		&-center {
			text-align: center;
		}

		&-right {
			text-align: right;
		}
	}

	.text-wrap {
		&-wrap {
			white-space: normal;
		}

		&-nowrap {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		&-pretty {
			white-space: normal;
			text-wrap: pretty;
		}

		&-balance {
			white-space: normal;
			text-wrap: balance;
		}
	}

	.text-color {
		&-default {
			color: var(--color-dark);
		}

		&-inherited {
			color: inherit;
		}

		&-secondary {
			color: var(--color-secondary-text);
		}
	}

	.text-weight {
		&-normal {
			font-weight: 400;
		}

		&-bold {
			font-weight: 700;
		}
	}

	.text-italic {
		font-style: italic;
	}
</style>
