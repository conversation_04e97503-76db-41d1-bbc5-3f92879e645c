<script lang="ts">
	import type { ButtonProps } from '$lib/components/primitives/Button/Button';
	import { buttonCva } from '$lib/components/primitives/Button/Button.cva';

	let { size = 'large', variant = 'primary', children, ...props }: ButtonProps = $props();
</script>

<button class={buttonCva({ variant, size })} {...props}>
	{@render children?.()}
</button>

<style lang="scss">
	.btn {
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 2em;
		font-weight: bold;
		transition: opacity 0.2s ease;
		gap: var(--spacing-xs);

		&:hover {
			opacity: 0.8;
		}
	}

	.btn-size {
		&-small {
			padding: var(--spacing-xs) var(--spacing-sm);
		}

		&-medium {
			padding: var(--spacing-s) var(--spacing-m);
		}

		&-large {
			padding: var(--spacing-m) var(--spacing-l);
		}
	}

	.btn-variant {
		&-primary {
			background-color: var(--color-primary);
			color: var(--color-light);
		}

		&-error {
			background-color: var(--color-error);
			color: var(--color-light);
		}

		&-success {
			background-color: var(--color-success);
			color: var(--color-light);
		}
	}
</style>
