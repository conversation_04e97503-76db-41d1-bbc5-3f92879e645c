import { cva } from 'class-variance-authority';

export const headingCva = cva('heading', {
	variants: {
		as: {
			h1: '',
			h2: '',
			h3: '',
			h4: '',
			h5: '',
			h6: ''
		},
		size: {
			hero: 'heading-size-hero',
			h1: 'heading-size-h1',
			h2: 'heading-size-h2',
			h3: 'heading-size-h3',
			h4: 'heading-size-h4',
			h5: 'heading-size-h5',
			h6: 'heading-size-h6'
		},
		italic: {
			true: 'heading-italic',
			false: ''
		},
		align: {
			left: 'heading-align-left',
			center: 'heading-align-center',
			right: 'heading-align-right'
		},
		wrap: {
			wrap: 'heading-wrap-wrap',
			nowrap: 'heading-wrap-nowrap',
			pretty: 'heading-wrap-pretty',
			balance: 'heading-wrap-balance'
		},
		color: {
			default: 'heading-color-default',
			inherited: 'heading-color-inherited'
		}
	},
	defaultVariants: {
		color: 'default',
		as: 'h1',
		size: 'h1',
		align: 'left',
		wrap: 'wrap',
		italic: false
	}
});
