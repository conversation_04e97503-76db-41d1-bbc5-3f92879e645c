<script lang="ts">
	import clsx from 'clsx';

	import type { HeadingProps } from '$lib/components/primitives/Heading/Heading';
	import { headingCva } from '$lib/components/primitives/Heading/Heading.cva';

	let { children, as, align, wrap, size, italic, color, ...props }: HeadingProps = $props();
</script>

<svelte:element
	this={as || (size === 'hero' ? 'h1' : size)}
	class={clsx(headingCva({ align, wrap, size, italic, color }))}
	{...props}
>
	{@render children?.()}
</svelte:element>

<style lang="scss">
	.heading-size {
		&-hero {
			font-size: var(--font-size-hero);
		}

		&-h1 {
			font-size: var(--font-size-1);
		}

		&-h2 {
			font-size: var(--font-size-2);
		}

		&-h3 {
			font-size: var(--font-size-3);
		}

		&-h4 {
			font-size: var(--font-size-4);
		}

		&-h5 {
			font-size: var(--font-size-text);
			font-weight: 700;
		}

		&-h6 {
			font-size: var(--font-size-sub);
			font-weight: 700;
			text-transform: uppercase;
			letter-spacing: 0.05em;
		}
	}

	.heading-align {
		&-left {
			text-align: left;
		}

		&-center {
			text-align: center;
		}

		&-right {
			text-align: right;
		}
	}

	.heading-wrap {
		&-wrap {
			white-space: normal;
		}

		&-nowrap {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		&-pretty {
			white-space: normal;
			text-wrap: pretty;
		}

		&-balance {
			white-space: normal;
			text-wrap: balance;
		}
	}

	.heading-color {
		&-default {
			color: var(--color-dark);
		}

		&-inherited {
			color: inherit;
		}
	}

	.heading-italic {
		font-style: italic;
	}
</style>
