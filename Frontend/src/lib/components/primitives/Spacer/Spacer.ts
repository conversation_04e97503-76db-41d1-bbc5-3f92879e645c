import type { VariantProps } from 'class-variance-authority';
import type { HTMLAttributes } from 'svelte/elements';

import type { spacerCva } from '$lib/components/primitives/Spacer/Spacer.cva';

type Variants = VariantProps<typeof spacerCva>;

export type SpacerDivProps = { as?: 'div' } & HTMLAttributes<HTMLDivElement> & Variants;

export type SpacerSpanProps = { as: 'span' } & HTMLAttributes<HTMLSpanElement> & Variants;

export type SpacerProps = SpacerDivProps | SpacerSpanProps;
