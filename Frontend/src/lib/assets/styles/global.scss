@use '_reset.scss';

html {
	font-family: 'Ubuntu', sans-serif;
	color-scheme: light;
	scroll-behavior: smooth;
}

body {
	background-color: var(--color-light);
	color: var(--color-dark);
	font-size: 16px;
	min-height: 100dvh;
	display: flex;
	flex-direction: column;
	font-synthesis: none;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

// Root variables
:root {
	// From Figma design
	--color-primary: #427ef4;
	--color-light: #ffffff;
	--color-dark: #1e1e1e;
	--color-section: #f6f6f6;
	--color-border: #e6e6e6;
	--color-secondary-text: #868686;
	--color-success: #ace997;
	--color-error: #f44242;

	--font-size-hero: 3.75rem; // 60px
	--font-size-1: 3rem; // 48px
	--font-size-2: 2rem; // 32px
	--font-size-3: 1.5rem; // 24px
	--font-size-4: 1.125rem; // 18px
	--font-size-text: 0.875rem; // 14px
	--font-size-small: 0.75rem; // 12px
	--font-size-sub: 0.625rem; // 10px

	--spacing-xs: 0.25rem; // 4px
	--spacing-s: 0.5rem; // 8px
	--spacing-sm: 0.75rem; // 12px
	--spacing-m: 1rem; // 16px
	--spacing-l: 1.5rem; // 24px
	--spacing-xl: 2rem; // 32px
	--spacing-xxl: 3rem; // 48px

	--border-radius: 0.5rem; // 8px
	--border-radius-lg: 1rem; // 16px
	--border-radius-xl: 1.5rem; // 24px

	--header-height: 3.25rem;
}
